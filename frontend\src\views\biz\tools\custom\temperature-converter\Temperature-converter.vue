<script setup lang="ts">
import _ from "lodash";
import { reactive, computed } from "vue";
import { convertCelsiusToKelvin, convertFahrenheitToKelvin, convertKelvinToCelsius, convertKelvinToFahrenheit } from "./temperature-converter.models";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

type TemperatureScale = "kelvin" | "celsius" | "fahrenheit";

// 分离数据和翻译，数据部分使用 reactive
const unitsData = reactive<
  Record<
    string | TemperatureScale,
    {
      ref: number;
      toKelvin: (v: number) => number;
      fromKelvin: (v: number) => number;
    }
  >
>({
  celsius: {
    ref: 20,
    toKelvin: convertCelsiusToKelvin,
    fromKelvin: convertKelvinToCelsius
  },
  fahrenheit: {
    ref: 68,
    toKelvin: convertFahrenheitToKelvin,
    fromKelvin: convertKelvinToFahrenheit
  },
  kelvin: {
    ref: 293.15,
    toKelvin: _.identity,
    fromKelvin: _.identity
  }
});

// 翻译部分使用 computed，确保语言切换时能响应式更新
const units = computed(() => ({
  celsius: {
    title: t("tools.temperature.celsius"),
    unit: t("tools.temperature.celsiusUnit"),
    icon: "🌡️",
    color: "#409EFF",
    ...unitsData.celsius
  },
  fahrenheit: {
    title: t("tools.temperature.fahrenheit"),
    unit: t("tools.temperature.fahrenheitUnit"),
    icon: "🌡️",
    color: "#E6A23C",
    ...unitsData.fahrenheit
  },
  kelvin: {
    title: t("tools.temperature.kelvin"),
    unit: t("tools.temperature.kelvinUnit"),
    icon: "🔬",
    color: "#67C23A",
    ...unitsData.kelvin
  }
}));

function update(key: TemperatureScale): void {
  const { ref: value, toKelvin } = units.value[key];

  const kelvins = toKelvin(value) ?? 0;

  _.chain(units.value)
    .omit(key)
    .forEach((unit: { fromKelvin: (v: number) => number }, unitKey: string) => {
      unitsData[unitKey as TemperatureScale].ref = Math.round((unit.fromKelvin(kelvins) ?? 0) * 100) / 100;
    })
    .value();
}

// 设置特定温度值
function setTemperature(scale: TemperatureScale, value: number): void {
  unitsData[scale].ref = value;
  update(scale);
}

// 初始化为室温（20°C）
update("celsius");
</script>

<template>
  <div class="temperature-converter-container">
    <div class="header">
      <div class="title">
        <span class="title-icon">🌡️</span>
        {{ t("tools.temperature.title") }}
      </div>
      <div class="description">{{ t("tools.temperature.description") }}</div>
    </div>

    <div class="converter-content">
      <div class="temperature-cards">
        <div v-for="[key, unit] in Object.entries(units)" :key="key" class="temperature-card" :style="{ '--card-color': unit.color }">
          <div class="card-header">
            <span class="card-icon">{{ unit.icon }}</span>
            <span class="card-title">{{ unit.title }}</span>
          </div>
          <div class="card-content">
            <div class="temperature-value">{{ (unitsData[key].ref ?? 0).toFixed(2) }}</div>
            <div class="unit-label">{{ unit.unit }}</div>
          </div>
        </div>
      </div>

      <div class="conversion-info">
        <div class="info-item">
          <span class="info-label">{{ t("tools.temperature.commonTemps") || "常见温度" }}:</span>
          <div class="temp-examples">
            <el-button size="small" @click="setTemperature('celsius', 0)">冰点 (0°C)</el-button>
            <el-button size="small" @click="setTemperature('celsius', 20)">室温 (20°C)</el-button>
            <el-button size="small" @click="setTemperature('celsius', 37)">体温 (37°C)</el-button>
            <el-button size="small" @click="setTemperature('celsius', 100)">沸点 (100°C)</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "@/styles/utils";

.temperature-converter-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background: var(--el-bg-color-page);
}

.header {
  margin-bottom: 32px;
  text-align: center;

  .title {
    @include flex(row, center, center);
    gap: 12px;
    margin-bottom: 12px;
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);

    .title-icon {
      font-size: 32px;
    }
  }

  .description {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.6;
    color: var(--el-text-color-regular);
  }
}

.converter-content {
  max-width: 800px;
  margin: 0 auto;
}

.temperature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.temperature-card {
  padding: 24px;
  background: var(--el-bg-color);
  border: 2px solid var(--el-border-color-light);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--card-color);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .card-header {
    @include flex(row, center, center);
    gap: 8px;
    margin-bottom: 20px;

    .card-icon {
      font-size: 24px;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .card-content {
    @include flex(column, center, center);
    gap: 12px;

    .temperature-input {
      width: 100%;

      :deep(.el-input__wrapper) {
        border-radius: 12px;
        box-shadow: 0 0 0 1px var(--card-color);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 2px var(--card-color);
        }

        &.is-focus {
          box-shadow: 0 0 0 2px var(--card-color);
        }
      }

      :deep(.el-input__inner) {
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        color: var(--card-color);
      }

      :deep(.el-input-number__increase),
      :deep(.el-input-number__decrease) {
        color: var(--card-color);

        &:hover {
          color: var(--card-color);
          background-color: rgba(64, 158, 255, 0.1);
        }
      }
    }

    .unit-label {
      font-size: 16px;
      font-weight: 600;
      color: var(--card-color);
    }
  }
}

.conversion-info {
  padding: 24px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;

  .info-item {
    .info-label {
      display: block;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .temp-examples {
      @include flex(row, flex-start, center);
      gap: 12px;
      flex-wrap: wrap;

      .el-button {
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .temperature-converter-container {
    padding: 16px;
  }

  .header {
    margin-bottom: 24px;

    .title {
      font-size: 24px;

      .title-icon {
        font-size: 28px;
      }
    }

    .description {
      font-size: 14px;
    }
  }

  .temperature-cards {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }

  .temperature-card {
    padding: 20px;
  }

  .conversion-info {
    padding: 20px;

    .temp-examples {
      justify-content: center;
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .temperature-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }
  }
}
</style>
